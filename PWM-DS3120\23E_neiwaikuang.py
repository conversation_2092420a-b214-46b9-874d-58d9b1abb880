import cv2
import numpy as np
import math
import time
import collections
import serial
import threading

# 导入MaixCAM专用硬件库
from maix import image, display, camera, app

# 配置参数
ENABLE_SERIAL = True
SERIAL_PORT = '/dev/ttyS0'
BAUD_RATE = 115200
POSITION_DISPLAY = True # 此参数在原代码中未直接使用，但保留

# 电工胶带参数（单位：像素，需要根据实际情况校准）
TAPE_WIDTH_PIXELS = 18  # 假设1.8cm胶带在图像中约为18像素宽
TAPE_WIDTH_TOLERANCE = 8  # 允许的误差范围

# 黑色电工胶带的HSV范围
# 这里的V值上限相对较低，因为黑色物体在HSV中V值较小
BLACK_TAPE_LOWER = np.array([0, 0, 0], dtype=np.uint8)
BLACK_TAPE_UPPER = np.array([180, 100, 50], dtype=np.uint8)

# 修改: 调整激光颜色范围为新提供的范围
# 红色激光范围
LOWER_RED1 = np.array([0, 65, 50], dtype=np.uint8)
UPPER_RED1 = np.array([10, 255, 255], dtype=np.uint8)
LOWER_RED2 = np.array([160, 65, 50], dtype=np.uint8)
UPPER_RED2 = np.array([180, 255, 255], dtype=np.uint8)

# 绿色激光范围
LOWER_GREEN = np.array([40, 100, 50], dtype=np.uint8)
UPPER_GREEN = np.array([80, 255, 255], dtype=np.uint8)

# 用于计算像素和的半径
PIXEL_RADIUS = 3

# 全局变量
inner_rectangle_corners = None  # 保存内框角点坐标
outer_rectangle_corners = None  # 保存外框角点坐标
send_rectangle_corners = False  # 是否需要发送角点坐标

# 添加：状态管理变量
DETECTION_STATE_RECTANGLE = 0    # 识别矩形状态
DETECTION_STATE_STABILIZING = 1  # 矩形稳定确认状态
DETECTION_STATE_LASER = 2        # 激光识别状态
detection_state = DETECTION_STATE_RECTANGLE  # 初始状态为识别矩形

# 添加：稳定性检测变量
stable_frame_count = 0           # 稳定帧计数
STABLE_FRAME_THRESHOLD = 5       # 需要连续稳定的帧数，从10改为5
last_inner_corners = None        # 上一帧的内框角点
last_outer_corners = None        # 上一帧的外框角点
saved_inner_corners = None       # 保存的稳定内框角点
saved_outer_corners = None       # 保存的稳定外框角点

def detect_black_tape_rectangles(image):
    """
    专门检测由黑色电工胶带形成的嵌套矩形结构（内外框）
    支持任意角度的旋转矩形，并专注于黑色电工胶带形成的矩形
    
    参数:
        image: 输入的OpenCV图像（BGR格式）
    
    返回:
        processed_image: 处理后的图像，包含标记
        inner_corners: 内框的四个角点坐标，如果未找到则为None
        outer_corners: 外框的四个角点坐标，如果未找到则为None
    """
    # 创建输出图像的副本
    output = image.copy()
    
    # 1. 颜色过滤 - 只保留黑色电工胶带区域
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    black_mask = cv2.inRange(hsv, BLACK_TAPE_LOWER, BLACK_TAPE_UPPER)
    
    # 添加形态学操作来清理黑色区域
    kernel = np.ones((3, 3), np.uint8)
    black_mask = cv2.morphologyEx(black_mask, cv2.MORPH_CLOSE, kernel)
    black_mask = cv2.morphologyEx(black_mask, cv2.MORPH_OPEN, kernel)
    
    # 将黑色过滤结果显示在调试窗口 (仅测试使用)
    cv2.putText(output, "Black tape filter active", (10, 210), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 2. 边缘检测 - 在黑色区域上检测边缘
    edges = cv2.Canny(black_mask, 50, 150)
    
    # 使用膨胀操作连接可能断开的边缘
    dilated_edges = cv2.dilate(edges, kernel, iterations=1)
    
    # 3. 轮廓检测
    contours, _ = cv2.findContours(dilated_edges, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        cv2.putText(output, "No contours in black regions", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        return output, None, None
    
    # 4. 识别潜在的矩形轮廓
    rectangle_candidates = []
    
    for contour in contours:
        # 过滤掉太小的轮廓
        area = cv2.contourArea(contour)
        if area < 1000:  # 根据实际情况调整
            continue
        
        # 周长
        peri = cv2.arcLength(contour, True)
        if peri == 0:
            continue
            
        # 使用多边形近似
        approx = cv2.approxPolyDP(contour, 0.02 * peri, True)
        
        # 检查是否接近四边形
        if len(approx) >= 4 and len(approx) <= 6:
            # 如果不是精确的四边形，使用最小面积矩形
            if len(approx) != 4:
                rect = cv2.minAreaRect(contour)
                box = cv2.boxPoints(rect)
                box = np.int0(box)
                approx = box.reshape(-1, 1, 2)
            
            # 计算矩形度 - 矩形面积与轮廓面积的比值
            rect_area = cv2.contourArea(approx)
            if rect_area == 0:
                continue
                
            rect_ratio = area / rect_area
            
            # 真正的矩形应该有较高的矩形度
            if rect_ratio > 0.8:
                # 使用最小面积包围矩形获取角度信息
                rect = cv2.minAreaRect(contour)
                
                # 检查边缘黑度 - 在原图中检查轮廓边缘是否真的是黑色
                edge_blackness = check_edge_blackness(image, approx)
                
                # 计算长宽比
                width, height = rect[1]
                if width == 0 or height == 0:
                    continue
                    
                aspect_ratio = max(width, height) / min(width, height)
                
                rectangle_candidates.append({
                    'contour': contour,
                    'approx': approx,
                    'area': area,
                    'rect': rect,
                    'rect_ratio': rect_ratio,
                    'edge_blackness': edge_blackness,
                    'aspect_ratio': aspect_ratio
                })
    
    # 根据黑度、矩形度和面积排序候选矩形
    if rectangle_candidates:
        # 首先按照边缘黑度排序
        rectangle_candidates.sort(key=lambda r: r['edge_blackness'], reverse=True)
        
        # 从黑度较高的候选中再按照矩形度和面积筛选
        high_blackness_candidates = [r for r in rectangle_candidates if r['edge_blackness'] > 0.6]
        
        # 如果有足够的黑色矩形候选
        if len(high_blackness_candidates) >= 2:
            rectangle_candidates = high_blackness_candidates
        
        # 按面积排序
        rectangle_candidates.sort(key=lambda r: r['area'], reverse=True)
    
    # 如果没有足够的候选矩形
    if len(rectangle_candidates) < 2:
        cv2.putText(output, f"Found only {len(rectangle_candidates)} valid black rectangles", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        return output, None, None
    
    # 5. 识别内外框 - 基于嵌套关系和面积比例
    inner_rect = None
    outer_rect = None
    
    # 尝试所有可能的内外框组合
    best_nesting_score = -1
    
    for i, rect1 in enumerate(rectangle_candidates):
        for j, rect2 in enumerate(rectangle_candidates):
            if i == j:
                continue
            
            # 确定大小关系
            if rect1['area'] > rect2['area']:
                potential_outer = rect1
                potential_inner = rect2
            else:
                potential_outer = rect2
                potential_inner = rect1
            
            # 计算嵌套得分
            nesting_score = calculate_nesting_score(
                potential_inner['approx'], potential_outer['approx'])
            
            # 计算面积比和边缘距离
            area_ratio = potential_inner['area'] / potential_outer['area']
            edge_distance = calculate_edge_distance(
                potential_inner['approx'], potential_outer['approx'])
            
            # 综合得分：嵌套程度 + 合理的面积比 + 符合电工胶带宽度的边缘距离
            combined_score = nesting_score
            
            if 0.4 < area_ratio < 0.95:
                combined_score += 0.5
                
            if TAPE_WIDTH_PIXELS - TAPE_WIDTH_TOLERANCE <= edge_distance <= TAPE_WIDTH_PIXELS + TAPE_WIDTH_TOLERANCE:
                combined_score += 0.5
                
            # 更新最佳组合
            if combined_score > best_nesting_score:
                best_nesting_score = combined_score
                outer_rect = potential_outer
                inner_rect = potential_inner
    
    # 如果没有找到明显的嵌套关系
    if best_nesting_score < 0.6:
        cv2.putText(output, "No clear nesting relationship", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        return output, None, None
    
    # 6. 提取角点并按照指定规则排序
    inner_corners = None
    outer_corners = None
    
    if inner_rect:
        inner_points = np.array([p[0] for p in inner_rect['approx']])
        inner_corners = order_corners_top_clockwise(inner_points)
    
    if outer_rect:
        outer_points = np.array([p[0] for p in outer_rect['approx']])
        outer_corners = order_corners_top_clockwise(outer_points)
    
    # 7. 绘制外框 - 红色
    if outer_corners is not None:
        # 将点转换为整数类型并确保格式正确
        outer_points_int = np.int32(outer_corners.reshape(-1, 1, 2))
        cv2.polylines(output, [outer_points_int], True, (0, 0, 255), 2)
        
        # 标记外框角点
        for i, corner in enumerate(outer_corners):
            # 确保corner是整数坐标
            corner_int = tuple(np.int32(corner))
            cv2.circle(output, corner_int, 5, (0, 0, 255), -1)
            cv2.putText(output, f"O{i}", (corner_int[0]+5, corner_int[1]+5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
    
    # 8. 绘制内框 - 绿色
    if inner_corners is not None:
        # 将点转换为整数类型并确保格式正确
        inner_points_int = np.int32(inner_corners.reshape(-1, 1, 2))
        cv2.polylines(output, [inner_points_int], True, (0, 255, 0), 2)
        
        # 标记内框角点
        for i, corner in enumerate(inner_corners):
            # 确保corner是整数坐标
            corner_int = tuple(np.int32(corner))
            cv2.circle(output, corner_int, 5, (0, 255, 0), -1)
            cv2.putText(output, f"I{i}", (corner_int[0]+5, corner_int[1]+5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    
    # 9. 绘制信息
    cv2.putText(output, f"Outer Rectangle: {'Found' if outer_corners is not None else 'Not Found'}", 
               (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
    cv2.putText(output, f"Inner Rectangle: {'Found' if inner_corners is not None else 'Not Found'}", 
               (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    if outer_rect:
        cv2.putText(output, f"Nesting Score: {best_nesting_score:.2f}", (10, 90), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
    
    return output, inner_corners, outer_corners

def check_edge_blackness(image, approx):
    """
    检查轮廓边缘是否真的是黑色
    返回边缘黑色程度的比例(0-1)
    """
    h, w = image.shape[:2]
    mask = np.zeros((h, w), dtype=np.uint8)
    
    # 绘制轮廓，宽度为5像素，用于采样边缘像素
    cv2.polylines(mask, [approx], True, 255, 5)
    
    # 获取掩码中非零（即边缘）的像素坐标
    edge_points = np.where(mask > 0)
    
    # 提取这些点在原图中的颜色
    edge_colors = image[edge_points]
    
    if len(edge_colors) == 0:
        return 0
    
    # 黑色像素的RGB值应该较低。这里取RGB的最大值来判断"非黑"程度。
    average_color = np.mean(edge_colors, axis=0) # 计算BGR各通道的平均值
    max_color_value = np.max(average_color) # 找到R, G, B中最大的平均值
    
    # 将其归一化为0-1的评分：越接近0越黑，转换为0-1之间的黑色程度评分
    # 1.0 - (max_color_value / 255.0) 意味着max_color_value越小，blackness_score越高（越黑）
    blackness_score = 1.0 - (max_color_value / 255.0)
    
    return blackness_score

def calculate_nesting_score(inner_approx, outer_approx):
    """
    计算内矩形嵌套在外矩形内部的程度
    返回一个0-1的分数，1表示完全嵌套
    """
    # 假设图像大小与摄像头设置一致
    h, w = 480, 640 
    inner_mask = np.zeros((h, w), dtype=np.uint8)
    outer_mask = np.zeros((h, w), dtype=np.uint8)
    
    # 填充矩形区域，注意需要将approx的形状调整为[N, 2]以便fillPoly使用
    inner_points = inner_approx.reshape(-1, 2)
    outer_points = outer_approx.reshape(-1, 2)
    
    cv2.fillPoly(inner_mask, [np.int32(inner_points)], 1)
    cv2.fillPoly(outer_mask, [np.int32(outer_points)], 1)
    
    # 计算内框的像素面积
    inner_area = np.sum(inner_mask)
    if inner_area == 0:
        return 0
    
    # 计算内框有多少像素在外框内部（两者相交的面积）
    intersection = cv2.bitwise_and(inner_mask, outer_mask)
    intersection_area = np.sum(intersection)
    
    # 嵌套比例 = 相交面积 / 内框面积
    nesting_score = intersection_area / inner_area
    
    return nesting_score

def calculate_edge_distance(inner_approx, outer_approx):
    """
    计算内框和外框之间的平均边缘距离
    此函数仅为估算，实际边缘距离计算复杂，可能需要更精确的几何方法。
    此处计算内框角点到外框边的平均最短距离。
    """
    # 提取角点，并确保形状正确
    inner_points = inner_approx.reshape(-1, 2)
    outer_points = outer_approx.reshape(-1, 2)
    
    distances = []
    
    for i in range(4):
        p_inner = inner_points[i]
        
        # 找到内框点到外框各边的最短距离
        min_dist = float('inf')
        for j in range(4):
            # 外框的边由 (outer_points[j], outer_points[(j+1)%4]) 构成
            p1 = outer_points[j]
            p2 = outer_points[(j+1) % 4]
            dist = point_to_line_distance(p_inner, p1, p2)
            min_dist = min(min_dist, dist)
        
        distances.append(min_dist)
    
    # 返回平均距离
    if distances:
        return np.mean(distances)
    return 0

def point_to_line_distance(point, line_point1, line_point2):
    """
    计算点到直线的距离。
    此为点到无限延长直线的距离，而不是到线段的距离。
    """
    x0, y0 = point
    x1, y1 = line_point1
    x2, y2 = line_point2
    
    # 如果线段是一个点 (长度为0)，则距离是点到这个"点"的距离
    if x1 == x2 and y1 == y2:
        return np.sqrt((x0-x1)**2 + (y0-y1)**2)
    
    # 计算点到直线的距离公式： |(y2-y1)x0 - (x2-x1)y0 + x2y1 - y2x1| / sqrt((y2-y1)^2 + (x2-x1)^2)
    numerator = abs((y2-y1)*x0 - (x2-x1)*y0 + x2*y1 - y2*x1)
    denominator = np.sqrt((y2-y1)**2 + (x2-x1)**2)
    
    if denominator == 0: # 避免除以零，通常发生在x1=x2且y1=y2，但上面已处理
        return 0
    return numerator / denominator

def order_corners_top_clockwise(corners):
    """
    将矩形角点按照指定规则排序:
    1. y坐标最小的点为第一个点(0)
    2. 如果有多个y坐标最小的点，选择x坐标最小的点
    3. 从该点开始顺时针排序其他点 (即 tl, tr, br, bl 的通用顺时针顺序，然后旋转)
    """
    if corners is None or len(corners) != 4:
        return None
    
    corners = np.array(corners).reshape(-1, 2)
    
    # Step 1: 先将点排序为标准的天文台序 (top-left, top-right, bottom-right, bottom-left)
    # 这种方法对旋转的矩形也有效
    rect_ordered = np.zeros((4, 2), dtype="float32")

    # 通过 x+y 的和来区分左上角 (最小和) 和右下角 (最大和)
    s = corners.sum(axis=1)
    rect_ordered[0] = corners[np.argmin(s)]  # top-left
    rect_ordered[2] = corners[np.argmax(s)]  # bottom-right

    # 通过 x-y 的差来区分右上角 (最小差) 和左下角 (最大差)
    diff = np.diff(corners, axis=1)
    rect_ordered[1] = corners[np.argmin(diff)]  # top-right
    rect_ordered[3] = corners[np.argmax(diff)]  # bottom-left
    
    # Step 2: 找到符合用户指定规则的起始点 (y坐标最小，若y相同则x最小)
    min_y_val = rect_ordered[:, 1].min()
    # 找出所有y坐标等于最小y值的点
    candidates = rect_ordered[rect_ordered[:, 1] == min_y_val]
    
    # 在这些点中，选择x坐标最小的点作为起始点
    start_point = candidates[np.argmin(candidates[:, 0])]

    # Step 3: 找到起始点在 rect_ordered 中的索引，然后旋转数组
    start_idx = -1
    for i, pt in enumerate(rect_ordered):
        if np.array_equal(pt, start_point):
            start_idx = i
            break
    
    # 使用 np.roll 旋转数组，使起始点成为第一个元素
    # 如果 start_idx 是 0, 不需要旋转。如果是 1, 旋转 -1；如果是 2, 旋转 -2，以此类推。
    if start_idx != -1:
        final_ordered_corners = np.roll(rect_ordered, -start_idx, axis=0)
    else:
        # 如果找不到，则返回原始排序 (理论上不会发生，因为start_point必在rect_ordered中)
        final_ordered_corners = rect_ordered

    return final_ordered_corners

def serial_reader_thread(ser):
    """串口读取线程，监听主机发送的指令"""
    global send_rectangle_corners
    
    while app.running:
        try:
            if ser.in_waiting > 0:
                data = ser.read(ser.in_waiting).decode('utf-8', errors='ignore').strip() # 使用strip()移除空白符
                print(f"收到串口指令: '{data}'")
                
                if "JX" in data:
                    send_rectangle_corners = True
                    print("收到指令 'JX'，准备发送内外框角点坐标")
        except Exception as e:
            print(f"串口读取错误: {e}")
            time.sleep(1) # 错误发生时暂停，避免频繁报错
        
        time.sleep(0.01) # 适当延时，避免CPU占用过高

# 用您提供的更准确的激光点检测函数替换原来的函数
def detect_laser_points(image_bgr):
    """
    使用提供的更准确方法检测图像中的红色和绿色激光点
    
    参数:
        image_bgr: 输入的OpenCV图像（BGR格式）
    
    返回:
        red_laser_points: 红色激光点的 (x, y) 坐标列表
        green_laser_points: 绿色激光点的 (x, y) 坐标列表
    """
    # 转 HSV 进行颜色分割
    hsv = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2HSV)
    
    # 创建红色激光掩码 (合并两个红色范围)
    mask_red = cv2.bitwise_or(
        cv2.inRange(hsv, LOWER_RED1, UPPER_RED1),
        cv2.inRange(hsv, LOWER_RED2, UPPER_RED2)
    )
    
    # 创建绿色激光掩码
    mask_green = cv2.inRange(hsv, LOWER_GREEN, UPPER_GREEN)

    # 闭运算去噪
    kernel = np.ones((5, 5), np.uint8)
    mask_red = cv2.morphologyEx(mask_red, cv2.MORPH_CLOSE, kernel)
    mask_green = cv2.morphologyEx(mask_green, cv2.MORPH_CLOSE, kernel)
    
    # 创建调试视图
    debug_image = np.zeros((480, 640, 3), dtype=np.uint8)
    debug_image[:, :, 2] = mask_red  # 红色通道显示红色激光掩码
    debug_image[:, :, 1] = mask_green  # 绿色通道显示绿色激光掩码
    
    # 将调试图像叠加到原图的一角
    debug_size = (160, 120)  # 调试窗口大小
    debug_resized = cv2.resize(debug_image, debug_size)
    image_bgr[0:debug_size[1], 0:debug_size[0]] = debug_resized

    red_laser_points = []
    green_laser_points = []

    # 查找红色激光轮廓
    contours_red, _ = cv2.findContours(mask_red, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for cnt in contours_red:
        rect = cv2.minAreaRect(cnt)
        
        cx, cy = map(int, rect[0])
        # 计算 (cx,cy) 小区域的 R/G 和
        h, w = image_bgr.shape[:2]
        x0, y0 = max(0, cx - PIXEL_RADIUS), max(0, cy - PIXEL_RADIUS)
        x1, y1 = min(w-1, cx + PIXEL_RADIUS), min(h-1, cy + PIXEL_RADIUS)
        roi = image_bgr[y0:y1, x0:x1]
        if roi.size > 0:
            r_sum = int(roi[:, :, 2].sum())
            g_sum = int(roi[:, :, 1].sum())
            if r_sum > g_sum:
                red_laser_points.append((cx, cy))

    # 查找绿色激光轮廓
    contours_green, _ = cv2.findContours(mask_green, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for cnt in contours_green:
        rect = cv2.minAreaRect(cnt)
        w, h = map(int, rect[1])
        if w < 3 and h < 3:
            continue
            
        cx, cy = map(int, rect[0])
        # 计算 (cx,cy) 小区域的 R/G 和
        h, w = image_bgr.shape[:2]
        x0, y0 = max(0, cx - PIXEL_RADIUS), max(0, cy - PIXEL_RADIUS)
        x1, y1 = min(w-1, cx + PIXEL_RADIUS), min(h-1, cy + PIXEL_RADIUS)
        roi = image_bgr[y0:y1, x0:x1]
        if roi.size > 0:
            r_sum = int(roi[:, :, 2].sum())
            g_sum = int(roi[:, :, 1].sum())
            if g_sum > r_sum:
                green_laser_points.append((cx, cy))

    return red_laser_points, green_laser_points

# 添加：检查角点是否稳定的函数
def is_stable(current_corners, last_corners, threshold=5):
    """
    检查两帧之间的角点是否稳定（变化小于阈值）
    
    参数:
        current_corners: 当前帧的角点坐标
        last_corners: 上一帧的角点坐标
        threshold: 允许的每个点的最大像素位移
        
    返回:
        stable: 如果所有点的位移都小于阈值，则返回True
    """
    if current_corners is None or last_corners is None:
        return False
    
    # 确保两组角点都是numpy数组
    current_corners = np.array(current_corners)
    last_corners = np.array(last_corners)
    
    # 计算欧氏距离
    distances = np.sqrt(np.sum((current_corners - last_corners) ** 2, axis=1))
    
    # 如果所有距离都小于阈值，则认为稳定
    return np.all(distances < threshold)

def main():
    global inner_rectangle_corners, outer_rectangle_corners, send_rectangle_corners
    global detection_state, stable_frame_count, last_inner_corners, last_outer_corners
    global saved_inner_corners, saved_outer_corners
    
    # MaixCAM 硬件初始化
    cam = camera.Camera()
    disp = display.Display()
    
    # 设置摄像头参数
    cam.set_resolution(640, 480) # 设置摄像头分辨率
    
    # 初始化串口
    ser = None
    if ENABLE_SERIAL:
        try:
            ser = serial.Serial(SERIAL_PORT, BAUD_RATE, timeout=1)
            print(f"串口 {SERIAL_PORT} 打开成功")
            
            # 启动串口读取线程
            reader_thread = threading.Thread(target=serial_reader_thread, args=(ser,), daemon=True)
            reader_thread.start()
        except Exception as e:
            print(f"错误：无法打开串口 {SERIAL_PORT}: {e}")
    
    # 用于计算FPS
    last_time = time.time()
    frame_count = 0
    fps = 0
    
    # 用于记录时间戳的变量，以控制串口发送频率
    last_serial_send_time = time.time()
    SERIAL_SEND_INTERVAL = 0.05  # 每50ms发送一次，避免发送过频
    
    print("开始运行，初始状态：识别矩形...")
    
    # ---- 主循环 ----
    while app.running:
        # 1. 从摄像头读取一帧图像
        img_maix = cam.read()
        
        # 2. 将 maix.image 格式转换为 OpenCV 的 numpy.ndarray (BGR格式)
        frame = image.image2cv(img_maix)
        
        # 创建结果图像（用于显示状态和检测结果）
        result_image = frame.copy()
        
        # 当前时间（用于FPS计算和发送频率控制）
        current_time = time.time()
        
        # 根据不同状态执行不同的操作
        if detection_state == DETECTION_STATE_RECTANGLE:
            # 状态1：识别矩形内外框
            result_image, inner_corners, outer_corners = detect_black_tape_rectangles(frame)
            
            # 绘制状态信息
            cv2.putText(result_image, "State: Detecting Rectangle", (10, 420), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            
            # 如果同时检测到内外框，进入稳定性确认状态
            if inner_corners is not None and outer_corners is not None:
                detection_state = DETECTION_STATE_STABILIZING
                stable_frame_count = 0
                last_inner_corners = inner_corners.copy()
                last_outer_corners = outer_corners.copy()
                print("检测到内外框，进入稳定性确认状态...")
        
        elif detection_state == DETECTION_STATE_STABILIZING:
            # 状态2：矩形稳定性确认
            result_image, inner_corners, outer_corners = detect_black_tape_rectangles(frame)
            
            # 显示稳定帧计数
            cv2.putText(result_image, f"State: Stabilizing ({stable_frame_count}/{STABLE_FRAME_THRESHOLD})", (10, 420), 
                      cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            
            # 检查是否同时检测到内外框
            if inner_corners is not None and outer_corners is not None:
                # 检查与上一帧比较是否稳定
                inner_stable = is_stable(inner_corners, last_inner_corners)
                outer_stable = is_stable(outer_corners, last_outer_corners)
                
                if inner_stable and outer_stable:
                    stable_frame_count += 1
                    print(f"矩形稳定帧: {stable_frame_count}/{STABLE_FRAME_THRESHOLD}")
                    
                    # 如果达到稳定帧阈值，保存内外框坐标并转入激光检测状态
                    if stable_frame_count >= STABLE_FRAME_THRESHOLD:
                        saved_inner_corners = inner_corners.copy()
                        saved_outer_corners = outer_corners.copy()
                        detection_state = DETECTION_STATE_LASER
                        
                        # 发送一次保存的角点坐标
                        send_rectangle_corners = True
                        
                        print("矩形位置已稳定，保存坐标并开始激光检测...")
                else:
                    # 如果不稳定，重置计数器并更新上一帧角点
                    stable_frame_count = 0
                    print("矩形位置不稳定，重置稳定计数...")
                
                # 无论是否稳定，都更新上一帧角点
                last_inner_corners = inner_corners.copy()
                last_outer_corners = outer_corners.copy()
            else:
                # 如果有一个框没检测到，重置计数器
                stable_frame_count = 0
                print("未同时检测到内外框，重置稳定计数...")
        
        elif detection_state == DETECTION_STATE_LASER:
            # 状态3：激光检测状态
            
            # 绘制保存的矩形框（使用保存的坐标）
            if saved_outer_corners is not None:
                outer_points_int = np.int32(saved_outer_corners.reshape(-1, 1, 2))
                cv2.polylines(result_image, [outer_points_int], True, (0, 0, 255), 2)
                
                # 标记外框角点
                for i, corner in enumerate(saved_outer_corners):
                    corner_int = tuple(np.int32(corner))
                    cv2.circle(result_image, corner_int, 5, (0, 0, 255), -1)
                    cv2.putText(result_image, f"O{i}", (corner_int[0]+5, corner_int[1]+5), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
            
            if saved_inner_corners is not None:
                inner_points_int = np.int32(saved_inner_corners.reshape(-1, 1, 2))
                cv2.polylines(result_image, [inner_points_int], True, (0, 255, 0), 2)
                
                # 标记内框角点
                for i, corner in enumerate(saved_inner_corners):
                    corner_int = tuple(np.int32(corner))
                    cv2.circle(result_image, corner_int, 5, (0, 255, 0), -1)
                    cv2.putText(result_image, f"I{i}", (corner_int[0]+5, corner_int[1]+5), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            # 检测激光点
            red_lasers, green_lasers = detect_laser_points(frame)
            
            # 显示红色激光点并打印坐标
            if red_lasers:
                print(f"Red Lasers: {red_lasers}")
                for (x, y) in red_lasers:
                    # 确保坐标是整数
                    x, y = int(x), int(y)
                    cv2.circle(result_image, (x, y), 5, (0, 0, 255), -1) # 绘制红色圆点
                    cv2.putText(result_image, f"R({x},{y})", (x + 10, y - 10), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1) # 标注坐标
            
            # 显示绿色激光点并打印坐标
            if green_lasers:
                print(f"Green Lasers: {green_lasers}")
                for (x, y) in green_lasers:
                    # 确保坐标是整数
                    x, y = int(x), int(y)
                    cv2.circle(result_image, (x, y), 5, (0, 255, 0), -1) # 绘制绿色圆点
                    cv2.putText(result_image, f"G({x},{y})", (x + 10, y - 10), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1) # 标注坐标
            
            # 在激光检测状态下通过串口发送激光点坐标
            if ser and (current_time - last_serial_send_time) > SERIAL_SEND_INTERVAL:
                # 发送红色激光点坐标
                for rx, ry in red_lasers:
                    try:
                        red_msg = f"RDx{rx:03d}y{ry:03d}".encode('utf-8')
                        ser.write(red_msg)
                        time.sleep(0.01)  # 短暂延时，确保消息分开发送
                    except Exception as e:
                        print(f"发送红色激光点坐标错误: {e}")
                
                # 发送绿色激光点坐标
                for gx, gy in green_lasers:
                    try:
                        green_msg = f"GDx{gx:03d}y{gy:03d}".encode('utf-8')
                        ser.write(green_msg)
                        time.sleep(0.01)  # 短暂延时，确保消息分开发送
                    except Exception as e:
                        print(f"发送绿色激光点坐标错误: {e}")
                
                # 更新最后发送时间
                last_serial_send_time = current_time
            
            # 显示状态信息
            cv2.putText(result_image, "State: Detecting Laser", (10, 420), 
                      cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
        
        # 发送矩形角点坐标（如果需要且有有效坐标）
        if ser and send_rectangle_corners and (current_time - last_serial_send_time) > SERIAL_SEND_INTERVAL:
            corners_to_send = []
            
            # 根据当前状态决定使用哪个角点集合
            if detection_state == DETECTION_STATE_LASER:
                inner_to_send = saved_inner_corners
                outer_to_send = saved_outer_corners
            else:
                inner_to_send = inner_rectangle_corners
                outer_to_send = outer_rectangle_corners
            
            # 发送内框角点坐标（如果有）
            if inner_to_send is not None and len(inner_to_send) == 4:
                inner_corners_clipped = np.clip(inner_to_send, 0, [639, 479]).astype(int)
                
                inner_msg = "JXIax{:03d}y{:03d}bx{:03d}y{:03d}cx{:03d}y{:03d}dx{:03d}y{:03d}".format(
                    inner_corners_clipped[0][0], inner_corners_clipped[0][1], 
                    inner_corners_clipped[1][0], inner_corners_clipped[1][1],
                    inner_corners_clipped[2][0], inner_corners_clipped[2][1],
                    inner_corners_clipped[3][0], inner_corners_clipped[3][1]
                )
                corners_to_send.append(inner_msg)
            
            # 发送外框角点坐标（如果有）
            if outer_to_send is not None and len(outer_to_send) == 4:
                outer_corners_clipped = np.clip(outer_to_send, 0, [639, 479]).astype(int)
                
                outer_msg = "JXOax{:03d}y{:03d}bx{:03d}y{:03d}cx{:03d}y{:03d}dx{:03d}y{:03d}".format(
                    outer_corners_clipped[0][0], outer_corners_clipped[0][1], 
                    outer_corners_clipped[1][0], outer_corners_clipped[1][1],
                    outer_corners_clipped[2][0], outer_corners_clipped[2][1],
                    outer_corners_clipped[3][0], outer_corners_clipped[3][1]
                )
                corners_to_send.append(outer_msg)
            
            # 发送所有消息
            for msg in corners_to_send:
                try:
                    ser.write(msg.encode('utf-8'))
                    print(f"发送角点坐标: {msg}")
                    time.sleep(0.05)  # 短暂延时，确保消息分开发送
                except Exception as e:
                    print(f"串口写入错误: {e}")
                    break  # 发生错误则停止本次发送
            
            # 如果有消息被成功发送，重置标志位
            if corners_to_send:
                send_rectangle_corners = False
            
            # 更新最后发送时间
            last_serial_send_time = current_time
        
        # 计算并显示FPS
        frame_count += 1
        if current_time - last_time >= 1.0:
            fps = frame_count
            frame_count = 0
            last_time = current_time
        
        cv2.putText(result_image, f"FPS: {fps}", (10, 150), 
                  cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 将处理完的 OpenCV numpy.ndarray 转回 maix.image 格式
        img_show = image.cv2image(result_image)
        
        # 在MaixCAM的屏幕上显示最终画面
        disp.show(img_show)
    
    # 清理资源
    if ser:
        ser.close()
    print("程序退出")

# 启动程序
if __name__ == "__main__":
    main()

