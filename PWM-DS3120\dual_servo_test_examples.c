/**
  ******************************************************************************
  * @file    dual_servo_test_examples.c
  * @brief   双舵机控制测试示例代码
  ******************************************************************************
  */

#include "main.h"
#include "config.h"

/**
 * @brief 双舵机同步角度控制测试(180度+270度)
 */
void Test_DualServoSync(void) {
    // 同步测试基本角度控制
    Servo_SetAngle(0);      // 第一个舵机中位(0度)
    Servo2_SetAngle(135);   // 第二个舵机中位(135度)
    HAL_Delay(1000);

    Servo_SetAngle(-90);    // 第一个舵机最左(-90度)
    Servo2_SetAngle(0);     // 第二个舵机最小(0度)
    HAL_Delay(1000);

    Servo_SetAngle(90);     // 第一个舵机最右(90度)
    Servo2_SetAngle(270);   // 第二个舵机最大(270度)
    HAL_Delay(1000);

    Servo_SetAngle(0);      // 回中位
    Servo2_SetAngle(135);
    HAL_Delay(1000);
}

/**
 * @brief 双舵机协调运动测试(180度+270度)
 */
void Test_DualServoMirror(void) {
    // 协调运动测试：180度舵机从-90到90，270度舵机从270到0
    for(int16_t i = 0; i <= 18; i++) {
        int16_t angle1 = -90 + i * 10;          // 第一个舵机从-90到90
        int16_t angle2 = 270 - i * 15;          // 第二个舵机从270到0
        Servo_SetAngle(angle1);
        Servo2_SetAngle(angle2);
        HAL_Delay(200);
    }

    // 回到中位
    Servo_SetAngle(0);
    Servo2_SetAngle(135);
    HAL_Delay(1000);
}

/**
 * @brief 双舵机交替运动测试(180度+270度)
 */
void Test_DualServoAlternate(void) {
    // 交替运动测试
    for(int i = 0; i < 3; i++) {
        // 第一个舵机运动，第二个舵机保持中位
        Servo_SetAngle(-90);
        Servo2_SetAngle(135);
        HAL_Delay(500);

        Servo_SetAngle(90);
        Servo2_SetAngle(135);
        HAL_Delay(500);

        // 第二个舵机运动，第一个舵机保持中位
        Servo_SetAngle(0);
        Servo2_SetAngle(0);
        HAL_Delay(500);

        Servo_SetAngle(0);
        Servo2_SetAngle(270);
        HAL_Delay(500);
    }

    // 回到中位
    Servo_SetAngle(0);
    Servo2_SetAngle(135);
    HAL_Delay(1000);
}

/**
 * @brief 双舵机精确脉宽控制测试
 */
void Test_DualServoPrecisePulse(void) {
    // 使用精确脉宽控制
    Servo_SetPulse(1500);   // 第一个舵机1.5ms中位
    Servo2_SetPulse(1500);  // 第二个舵机1.5ms中位
    HAL_Delay(1000);
    
    Servo_SetPulse(1000);   // 第一个舵机1.0ms
    Servo2_SetPulse(2000);  // 第二个舵机2.0ms
    HAL_Delay(1000);
    
    Servo_SetPulse(2000);   // 第一个舵机2.0ms
    Servo2_SetPulse(1000);  // 第二个舵机1.0ms
    HAL_Delay(1000);
    
    Servo_SetPulse(1500);   // 回中位
    Servo2_SetPulse(1500);
    HAL_Delay(1000);
}

/**
 * @brief 双舵机圆周运动模拟测试
 */
void Test_DualServoCircular(void) {
    // 模拟圆周运动
    for(int i = 0; i < 360; i += 10) {
        float rad = i * 3.14159f / 180.0f;
        int16_t angle1 = (int16_t)(45 * sin(rad));      // 第一个舵机X轴
        int16_t angle2 = (int16_t)(45 * cos(rad));      // 第二个舵机Y轴
        
        Servo_SetAngle(angle1);
        Servo2_SetAngle(angle2);
        HAL_Delay(50);
    }
    
    // 回到中位
    Servo_SetAngle(0);
    Servo2_SetAngle(0);
    HAL_Delay(1000);
}

/**
 * @brief 双舵机速度对比测试
 */
void Test_DualServoSpeedCompare(void) {
    // 快速运动对比
    Servo_SetAngle(-90);
    Servo2_SetAngle(-90);
    HAL_Delay(100);
    
    Servo_SetAngle(90);
    Servo2_SetAngle(90);
    HAL_Delay(100);
    
    Servo_SetAngle(0);
    Servo2_SetAngle(0);
    HAL_Delay(500);
    
    // 慢速运动对比
    Servo_SetAngle(-45);
    Servo2_SetAngle(-45);
    HAL_Delay(1000);
    
    Servo_SetAngle(45);
    Servo2_SetAngle(45);
    HAL_Delay(1000);
    
    Servo_SetAngle(0);
    Servo2_SetAngle(0);
    HAL_Delay(1000);
}

/**
 * @brief 双舵机主测试函数
 */
void DualServo_RunAllTests(void) {
    Test_DualServoSync();
    HAL_Delay(2000);
    
    Test_DualServoMirror();
    HAL_Delay(2000);
    
    Test_DualServoAlternate();
    HAL_Delay(2000);
    
    Test_DualServoPrecisePulse();
    HAL_Delay(2000);
    
    Test_DualServoCircular();
    HAL_Delay(2000);
    
    Test_DualServoSpeedCompare();
    HAL_Delay(2000);
}
