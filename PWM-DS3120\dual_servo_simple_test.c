/**
  ******************************************************************************
  * @file    dual_servo_simple_test.c
  * @brief   双舵机简单测试程序 - 替换main.c中的测试代码
  ******************************************************************************
  */

#include "main.h"
#include "config.h"

/**
 * @brief 双舵机基本功能测试(180度+270度)
 * 将此函数内容复制到main.c的while循环中进行测试
 */
void DualServo_SimpleTest(void) {
    // 测试1: 双舵机运动到中位
    Servo_SetAngle(0);      // 180度舵机中位
    Servo2_SetAngle(135);   // 270度舵机中位
    HAL_Delay(1000);

    // 测试2: 第一个舵机左极限，第二个舵机0度
    Servo_SetAngle(-90);
    Servo2_SetAngle(0);
    HAL_Delay(1000);

    // 测试3: 第一个舵机右极限，第二个舵机270度
    Servo_SetAngle(90);
    Servo2_SetAngle(270);
    HAL_Delay(1000);

    // 测试4: 交叉位置1
    Servo_SetAngle(-45);
    Servo2_SetAngle(90);
    HAL_Delay(1000);

    // 测试5: 交叉位置2
    Servo_SetAngle(45);
    Servo2_SetAngle(225);
    HAL_Delay(1000);

    // 回到中位
    Servo_SetAngle(0);
    Servo2_SetAngle(135);
    HAL_Delay(1000);
}

/**
 * @brief 双舵机连续扫描测试
 */
void DualServo_SweepTest(void) {
    // 同步扫描
    for(int16_t angle = -90; angle <= 90; angle += 15) {
        Servo_SetAngle(angle);
        Servo2_SetAngle(angle);
        HAL_Delay(200);
    }
    
    // 镜像扫描
    for(int16_t angle = -90; angle <= 90; angle += 15) {
        Servo_SetAngle(angle);
        Servo2_SetAngle(-angle);
        HAL_Delay(200);
    }
    
    // 回到中位
    Servo_SetAngle(0);
    Servo2_SetAngle(0);
    HAL_Delay(1000);
}

/**
 * @brief 双舵机精确脉宽测试
 */
void DualServo_PulseTest(void) {
    // 中位脉宽测试
    Servo_SetPulse(1500);
    Servo2_SetPulse(1500);
    HAL_Delay(1000);
    
    // 最小脉宽测试
    Servo_SetPulse(500);
    Servo2_SetPulse(500);
    HAL_Delay(1000);
    
    // 最大脉宽测试
    Servo_SetPulse(2500);
    Servo2_SetPulse(2500);
    HAL_Delay(1000);
    
    // 不同脉宽测试
    Servo_SetPulse(1000);
    Servo2_SetPulse(2000);
    HAL_Delay(1000);
    
    // 回到中位
    Servo_SetPulse(1500);
    Servo2_SetPulse(1500);
    HAL_Delay(1000);
}

/**
 * @brief 在main.c的while循环中使用的完整测试序列
 * 复制以下代码到main.c的while(1)循环中:
 * 
 * // 基本功能测试
 * DualServo_SimpleTest();
 * HAL_Delay(2000);
 * 
 * // 连续扫描测试  
 * DualServo_SweepTest();
 * HAL_Delay(2000);
 * 
 * // 精确脉宽测试
 * DualServo_PulseTest();
 * HAL_Delay(2000);
 */
