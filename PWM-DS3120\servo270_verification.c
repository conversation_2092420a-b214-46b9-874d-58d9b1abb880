/**
  ******************************************************************************
  * @file    servo270_verification.c
  * @brief   270度舵机配置验证程序
  ******************************************************************************
  */

#include "main.h"
#include "config.h"

/**
 * @brief 验证270度舵机角度转换公式
 * 打印各个角度对应的脉宽值，用于验证配置正确性
 */
void Verify_Servo270_AngleConversion(void) {
    // 验证关键角度点的脉宽计算
    uint16_t test_angles[] = {0, 45, 90, 135, 180, 225, 270};
    uint8_t num_angles = sizeof(test_angles) / sizeof(test_angles[0]);
    
    // 这里只是计算验证，实际使用时可以通过串口输出或LED指示
    for(uint8_t i = 0; i < num_angles; i++) {
        uint16_t angle = test_angles[i];
        uint16_t expected_pulse = SERVO2_MIN_PULSE + (angle * (SERVO2_MAX_PULSE - SERVO2_MIN_PULSE) / 270);
        
        // 验证计算结果
        // 0度 -> 500us, 135度 -> 1500us, 270度 -> 2500us
        // 实际项目中可以通过串口输出这些值进行验证
    }
}

/**
 * @brief 270度舵机关键位置测试
 * 测试270度舵机的关键角度位置
 */
void Test_Servo270_KeyPositions(void) {
    // 测试0度位置
    Servo2_SetAngle(0);
    HAL_Delay(1000);
    
    // 测试90度位置(1/3位置)
    Servo2_SetAngle(90);
    HAL_Delay(1000);
    
    // 测试135度位置(中位)
    Servo2_SetAngle(135);
    HAL_Delay(1000);
    
    // 测试180度位置(2/3位置)
    Servo2_SetAngle(180);
    HAL_Delay(1000);
    
    // 测试270度位置(最大位置)
    Servo2_SetAngle(270);
    HAL_Delay(1000);
    
    // 回到中位
    Servo2_SetAngle(135);
    HAL_Delay(1000);
}

/**
 * @brief 270度舵机脉宽验证测试
 * 直接使用脉宽控制验证配置
 */
void Test_Servo270_PulseVerification(void) {
    // 测试最小脉宽(0度)
    Servo2_SetPulse(500);
    HAL_Delay(1000);
    
    // 测试中位脉宽(135度)
    Servo2_SetPulse(1500);
    HAL_Delay(1000);
    
    // 测试最大脉宽(270度)
    Servo2_SetPulse(2500);
    HAL_Delay(1000);
    
    // 测试1/4位置脉宽(约67.5度)
    Servo2_SetPulse(1000);
    HAL_Delay(1000);
    
    // 测试3/4位置脉宽(约202.5度)
    Servo2_SetPulse(2000);
    HAL_Delay(1000);
    
    // 回到中位
    Servo2_SetPulse(1500);
    HAL_Delay(1000);
}

/**
 * @brief 180度+270度舵机对比测试
 * 对比两个舵机的运动范围和特性
 */
void Test_ServoComparison(void) {
    // 两个舵机都设置到各自的中位
    Servo_SetAngle(0);      // 180度舵机中位
    Servo2_SetAngle(135);   // 270度舵机中位
    HAL_Delay(2000);
    
    // 180度舵机从-90到90，270度舵机从0到270
    // 分10步完成
    for(int i = 0; i <= 10; i++) {
        int16_t angle1 = -90 + i * 18;      // 180度舵机: -90到90
        int16_t angle2 = i * 27;            // 270度舵机: 0到270
        
        Servo_SetAngle(angle1);
        Servo2_SetAngle(angle2);
        HAL_Delay(500);
    }
    
    // 回到中位
    Servo_SetAngle(0);
    Servo2_SetAngle(135);
    HAL_Delay(1000);
}

/**
 * @brief 270度舵机边界测试
 * 测试角度限制功能
 */
void Test_Servo270_BoundaryTest(void) {
    // 测试超出下限的角度(应该被限制为0)
    Servo2_SetAngle(-50);   // 应该被限制为0度
    HAL_Delay(1000);
    
    // 测试超出上限的角度(应该被限制为270)
    Servo2_SetAngle(350);   // 应该被限制为270度
    HAL_Delay(1000);
    
    // 测试正常范围内的角度
    Servo2_SetAngle(100);
    HAL_Delay(1000);
    
    Servo2_SetAngle(200);
    HAL_Delay(1000);
    
    // 回到中位
    Servo2_SetAngle(135);
    HAL_Delay(1000);
}

/**
 * @brief 270度舵机完整验证程序
 * 运行所有验证测试
 */
void Servo270_RunVerification(void) {
    // 验证角度转换公式
    Verify_Servo270_AngleConversion();
    
    // 测试关键位置
    Test_Servo270_KeyPositions();
    HAL_Delay(2000);
    
    // 验证脉宽控制
    Test_Servo270_PulseVerification();
    HAL_Delay(2000);
    
    // 对比测试
    Test_ServoComparison();
    HAL_Delay(2000);
    
    // 边界测试
    Test_Servo270_BoundaryTest();
    HAL_Delay(2000);
}

/**
 * @brief 在main.c中使用的简化验证测试
 * 复制以下代码到main.c的while(1)循环中进行验证:
 * 
 * // 270度舵机验证测试
 * Test_Servo270_KeyPositions();
 * HAL_Delay(3000);
 * 
 * // 或运行完整验证
 * Servo270_RunVerification();
 */
