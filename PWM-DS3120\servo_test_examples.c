/**
  ******************************************************************************
  * @file    servo_test_examples.c
  * @brief   舵机控制测试示例代码
  ******************************************************************************
  */

#include "main.h"

/**
 * @brief 基本角度控制测试
 */
void Test_BasicAngleControl(void) {
    // 测试基本角度控制
    Servo_SetAngle(0);      // 中位
    HAL_Delay(1000);
    
    Servo_SetAngle(-90);    // 最左
    HAL_Delay(1000);
    
    Servo_SetAngle(90);     // 最右
    HAL_Delay(1000);
    
    Servo_SetAngle(0);      // 回中位
    HAL_Delay(1000);
}

/**
 * @brief 连续扫描测试
 */
void Test_ContinuousSweep(void) {
    // 从左到右扫描
    for(int16_t angle = -90; angle <= 90; angle += 5) {
        Servo_SetAngle(angle);
        HAL_Delay(50);
    }
    
    // 从右到左扫描
    for(int16_t angle = 90; angle >= -90; angle -= 5) {
        Servo_SetAngle(angle);
        HAL_Delay(50);
    }
}

/**
 * @brief 精确脉宽控制测试
 */
void Test_PrecisePulseControl(void) {
    // 使用精确脉宽控制
    Servo_SetPulse(1500);   // 1.5ms中位
    HAL_Delay(1000);
    
    Servo_SetPulse(1000);   // 1.0ms
    HAL_Delay(1000);
    
    Servo_SetPulse(2000);   // 2.0ms
    HAL_Delay(1000);
    
    Servo_SetPulse(1500);   // 回中位
    HAL_Delay(1000);
}

/**
 * @brief 微调测试
 */
void Test_FineTuning(void) {
    // 在中位附近微调
    for(uint16_t pulse = 1400; pulse <= 1600; pulse += 10) {
        Servo_SetPulse(pulse);
        HAL_Delay(100);
    }
    
    // 回到中位
    Servo_SetPulse(1500);
    HAL_Delay(500);
}

/**
 * @brief 速度测试
 */
void Test_SpeedControl(void) {
    // 快速运动
    Servo_SetAngle(-90);
    HAL_Delay(100);
    Servo_SetAngle(90);
    HAL_Delay(100);
    Servo_SetAngle(0);
    HAL_Delay(100);
    
    // 慢速运动
    Servo_SetAngle(-45);
    HAL_Delay(1000);
    Servo_SetAngle(45);
    HAL_Delay(1000);
    Servo_SetAngle(0);
    HAL_Delay(1000);
}

/**
 * @brief 主测试函数 - 替换main.c中的测试代码
 */
void Servo_RunAllTests(void) {
    Test_BasicAngleControl();
    HAL_Delay(2000);
    
    Test_ContinuousSweep();
    HAL_Delay(2000);
    
    Test_PrecisePulseControl();
    HAL_Delay(2000);
    
    Test_FineTuning();
    HAL_Delay(2000);
    
    Test_SpeedControl();
    HAL_Delay(2000);
}
