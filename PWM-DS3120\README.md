# PWM双舵机控制项目(180度+270度)

## 项目概述
基于STM32F103的PWM双舵机控制系统，支持180度和270度舵机的精确角度控制和协调操作。

## 硬件配置
- **MCU**: STM32F103C8T6
- **系统时钟**: 8MHz (HSI)
- **第一个舵机PWM输出引脚**: PA6 (TIM3_CH1) - 180度舵机
- **第二个舵机PWM输出引脚**: PB6 (TIM4_CH1) - 270度舵机
- **第一个舵机型号**: DS3120或兼容的180度标准舵机
- **第二个舵机型号**: 270度舵机

## PWM信号规格

### 第一个舵机(180度舵机)
- **周期**: 20ms (50Hz)
- **脉宽范围**: 0.5ms - 2.5ms
- **角度对应关系**:
  - 0.5ms → -90°
  - 1.5ms → 0°
  - 2.5ms → +90°

### 第二个舵机(270度舵机)
- **周期**: 20ms (50Hz)
- **脉宽范围**: 0.5ms - 2.5ms
- **角度对应关系**:
  - 0.5ms → 0°
  - 1.5ms → 135°
  - 2.5ms → 270°

## 核心功能

### 1. 第一个舵机控制函数
```c
void Servo_SetAngle(int16_t angle);   // 角度控制
void Servo_SetPulse(uint16_t pulse_us); // 脉宽控制
```

### 2. 第二个舵机控制函数(270度舵机)
```c
void Servo2_SetAngle(int16_t angle);   // 角度控制
void Servo2_SetPulse(uint16_t pulse_us); // 脉宽控制
```

- **参数**:
  - 第一个舵机: angle (-90° 到 +90°), pulse_us (500-2500微秒)
  - 第二个舵机: angle (0° 到 270°), pulse_us (500-2500微秒)
- **功能**: 设置舵机转动到指定角度或直接设置PWM脉宽
- **使用示例**:
```c
// 单舵机控制
Servo_SetAngle(0);    // 第一个舵机转到中位(0度)
Servo2_SetAngle(135); // 第二个舵机转到中位(135度)

// 双舵机协调控制
Servo_SetAngle(-90);  // 第一个舵机转到-90度
Servo2_SetAngle(0);   // 第二个舵机转到0度

Servo_SetAngle(90);   // 第一个舵机转到90度
Servo2_SetAngle(270); // 第二个舵机转到270度

// 精确脉宽控制
Servo_SetPulse(1500); // 第一个舵机1.5ms脉宽(0度)
Servo2_SetPulse(1500);// 第二个舵机1.5ms脉宽(135度)
```

### 3. 配置参数 (config.h)
```c
/* 第一个舵机控制配置(180度舵机) */
#define SERVO_PWM_PERIOD    20000     // PWM周期20ms
#define SERVO_MIN_PULSE     500       // 最小脉宽0.5ms对应-90度
#define SERVO_MAX_PULSE     2500      // 最大脉宽2.5ms对应+90度
#define SERVO_MID_PULSE     1500      // 中位脉宽1.5ms对应0度
#define SERVO_MIN_ANGLE     -90       // 最小角度
#define SERVO_MAX_ANGLE     90        // 最大角度

/* 第二个舵机控制配置(270度舵机) */
#define SERVO2_MIN_PULSE    500       // 最小脉宽0.5ms对应0度
#define SERVO2_MAX_PULSE    2500      // 最大脉宽2.5ms对应270度
#define SERVO2_MID_PULSE    1500      // 中位脉宽1.5ms对应135度
#define SERVO2_MIN_ANGLE    0         // 最小角度0度
#define SERVO2_MAX_ANGLE    270       // 最大角度270度
```

## 使用方法

### 基本使用
1. 调用 `Servo_SetAngle(angle)` 设置第一个舵机角度(-90° 到 +90°)
2. 调用 `Servo2_SetAngle(angle)` 设置第二个舵机角度(0° 到 270°)
3. 函数自动限制角度范围，防止超出限制

### 双舵机协调控制示例(180度+270度)
```c
// 双舵机协调扫描运动
for(int16_t i = 0; i <= 18; i++) {
    int16_t angle1 = -90 + i * 10;    // 第一个舵机从-90到90
    int16_t angle2 = i * 15;          // 第二个舵机从0到270
    Servo_SetAngle(angle1);
    Servo2_SetAngle(angle2);
    HAL_Delay(200);
}
```

### 270度舵机全范围扫描示例
```c
// 270度舵机全范围扫描
for(int16_t angle = 0; angle <= 270; angle += 15) {
    Servo2_SetAngle(angle);  // 第二个舵机从0到270度
    HAL_Delay(100);
}
```

### 双舵机交替控制示例
```c
Servo_SetAngle(-90);  // 第一个舵机转到-90度
Servo2_SetAngle(135); // 第二个舵机保持中位
HAL_Delay(500);
Servo_SetAngle(0);    // 第一个舵机回中位
Servo2_SetAngle(270); // 第二个舵机转到270度
HAL_Delay(500);
```

## 技术特点
- **混合舵机支持**: 同时控制180度和270度舵机，支持协调和独立操作
- **高精度**: 1μs分辨率的PWM控制
- **分离配置**: 180度和270度舵机独立配置参数
- **安全限制**: 自动限制各自角度范围
- **中文友好**: 完整的中文注释
- **高效实现**: 最少代码行数实现完整功能

## 编译和烧录
1. 使用Keil MDK-ARM打开项目
2. 编译项目 (Ctrl+F7)
3. 连接ST-Link调试器
4. 下载程序到MCU (F8)

## 测试程序

### 内置测试 (main.c)
当前main函数包含双舵机协调测试程序:
- 第一个舵机转到中位(0度)
- 第二个舵机转到中位(135度)
- 每秒循环一次

### 单舵机测试示例 (servo_test_examples.c)
提供了单舵机完整的测试示例代码:
1. **基本角度控制测试**: 测试-90°、0°、90°基本位置
2. **连续扫描测试**: 平滑的左右扫描运动
3. **精确脉宽控制测试**: 使用微秒级精确控制
4. **微调测试**: 在中位附近进行精细调整
5. **速度测试**: 快速和慢速运动对比

### 双舵机测试示例 (dual_servo_test_examples.c)
提供了180度+270度双舵机专用测试示例代码:
1. **双舵机协调测试**: 两个舵机协调运动
2. **双舵机协调运动测试**: 180度舵机配合270度舵机运动
3. **双舵机交替测试**: 两个舵机交替运动
4. **双舵机精确脉宽测试**: 使用微秒级精确控制
5. **双舵机圆周运动模拟**: 模拟XY轴圆周运动
6. **双舵机速度对比测试**: 快速和慢速运动对比

### 270度舵机专用测试示例 (servo270_test_examples.c)
提供了270度舵机专用测试示例代码:
1. **270度舵机基本角度控制**: 测试0°、135°、270°等关键位置
2. **270度舵机连续扫描**: 0度到270度全范围扫描
3. **270度舵机精确脉宽控制**: 使用微秒级精确控制
4. **270度舵机分段测试**: 分段测试各个角度区间
5. **270度舵机微调测试**: 在中位附近精细调整
6. **混合舵机协调测试**: 180度+270度舵机协调运动

使用方法:
```c
// 在main.c的while循环中调用单舵机测试
Servo_RunAllTests();

// 或调用双舵机协调测试
DualServo_RunAllTests();

// 或调用270度舵机专用测试
Servo270_RunAllTests();

// 或调用简单的270度舵机测试
Servo270_SimpleTest();
```

## 注意事项
1. 确保舵机电源充足(通常需要5V独立供电)
2. 第一个舵机(180度)信号线连接到PA6引脚(TIM3_CH1)
3. 第二个舵机(270度)信号线连接到PB6引脚(TIM4_CH1)
4. 舵机地线与MCU共地
5. 第一个舵机角度范围：-90° 到 +90°
6. 第二个舵机角度范围：0° 到 270°
7. 修改角度范围需同时更新config.h中对应的参数
8. 双舵机同时工作时注意电源负载能力

## 版本信息
- **版本**: v2.1 (180度+270度混合舵机版本)
- **更新日期**: 2025-01-20
- **兼容性**: STM32F1xx系列
- **新增功能**: 270度舵机支持，混合舵机协调控制
